/**
 * Search a particular variable in a list
 * @param {Array} variables The variables list.
 * @param {string} name The name of the variable to search.
 * @returns {boolean} True if the variable was found, false if not.
 */
export function findVariable(variables: any[], name: string): boolean;
/**
 * Find a variable by name in the current scope.
 * @param {Object} context The current rule context.
 * @param {ASTNode} node The node to check. Must be an Identifier node.
 * @param  {string} name Name of the variable to look for.
 * @returns {ASTNode|null} Return null if the variable could not be found, ASTNode otherwise.
 */
export function findVariableByName(context: any, node: ASTNode, name: string): ASTNode | null;
/**
 * Find and return a particular variable in a list
 * @param {Array} variables The variables list.
 * @param {string} name The name of the variable to search.
 * @returns {Object} Variable if the variable was found, null if not.
 */
export function getVariable(variables: any[], name: string): any;
/**
 * Searches for a variable in the given scope.
 *
 * @param {Object} context The current rule context.
 * @param {ASTNode} node The node to start looking from.
 * @param {string} name The name of the variable to search.
 * @returns {Object | undefined} Variable if the variable was found, undefined if not.
 */
export function getVariableFromContext(context: any, node: ASTNode, name: string): any | undefined;
/**
 * Returns the latest definition of the variable.
 * @param {Object} variable
 * @returns {Object | undefined} The latest variable definition or undefined.
 */
export function getLatestVariableDefinition(variable: any): any | undefined;
//# sourceMappingURL=variable.d.ts.map